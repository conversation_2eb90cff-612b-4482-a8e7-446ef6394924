#!/usr/bin/env python3

import sys
# use line-buffering for both stdout and stderr
sys.stdout = open(sys.stdout.fileno(), mode='w', buffering=1)
sys.stderr = open(sys.stderr.fileno(), mode='w', buffering=1)

import hydra
import time
from omegaconf import OmegaConf
import pathlib
from diffusion_policy_3d.workspace.base_workspace import BaseWorkspace
import tqdm
import torch
import os 
import numpy as np
from termcolor import cprint
from torch.utils.data import DataLoader
from diffusion_policy_3d.common.pytorch_util import dict_apply
import copy

os.environ['WANDB_SILENT'] = "True"
# allows arbitrary python code execution in configs using the ${eval:''} resolver
OmegaConf.register_new_resolver("eval", eval, replace=True)


def evaluate_model_on_dataset(workspace, cfg):
    """
    在数据集上评估训练好的模型
    """
    cprint("=" * 50, "green")
    cprint("开始模型评估", "green")
    cprint("=" * 50, "green")
    
    # 加载模型
    policy = workspace.get_model()
    policy.eval()

    # 确保模型在正确的设备上
    device = torch.device(cfg.training.device)
    policy = policy.to(device)
    
    # 配置数据集
    dataset = hydra.utils.instantiate(cfg.task.dataset)
    
    # 获取验证数据集
    val_dataset = dataset.get_validation_dataset()
    if len(val_dataset) == 0:
        cprint("警告: 验证数据集为空，使用训练数据集进行评估", "yellow")
        val_dataset = dataset
    
    val_dataloader = DataLoader(val_dataset, **cfg.val_dataloader)
    
    cprint(f"数据集大小: {len(val_dataset)}", "cyan")
    cprint(f"批次大小: {cfg.val_dataloader.batch_size}", "cyan")
    
    # 评估指标
    all_mse_errors = []
    all_losses = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm.tqdm(val_dataloader, desc="评估中")):
            # 数据转移到设备 - 递归处理嵌套字典
            def to_device_recursive(obj):
                if isinstance(obj, torch.Tensor):
                    return obj.to(device, non_blocking=True)
                elif isinstance(obj, dict):
                    return {k: to_device_recursive(v) for k, v in obj.items()}
                elif isinstance(obj, (list, tuple)):
                    return type(obj)(to_device_recursive(item) for item in obj)
                else:
                    return obj

            batch = to_device_recursive(batch)
            
            obs_dict = batch['obs']
            gt_action = batch['action']
            
            # 模型推理
            try:
                # 检查数据设备
                for key, value in obs_dict.items():
                    if isinstance(value, torch.Tensor):
                        if value.device != device:
                            cprint(f"警告: {key} 在设备 {value.device}，期望在 {device}", "yellow")

                result = policy.predict_action(obs_dict)
                pred_action = result['action_pred']

                # 计算MSE误差
                mse = torch.nn.functional.mse_loss(pred_action, gt_action)
                all_mse_errors.append(mse.item())

                # 计算模型损失
                loss = policy.compute_loss(batch)
                all_losses.append(loss.item())

                if batch_idx == 0:
                    cprint(f"第一个批次成功处理", "green")

            except Exception as e:
                cprint(f"批次 {batch_idx} 评估失败: {e}", "red")
                import traceback
                cprint(f"详细错误: {traceback.format_exc()}", "red")
                continue
            
            # 限制评估批次数量（可选）
            if cfg.training.max_val_steps is not None and batch_idx >= cfg.training.max_val_steps - 1:
                break
    
    # 计算统计结果
    if all_mse_errors:
        mean_mse = np.mean(all_mse_errors)
        std_mse = np.std(all_mse_errors)
        mean_loss = np.mean(all_losses)
        std_loss = np.std(all_losses)
        
        cprint("=" * 50, "green")
        cprint("评估结果", "green")
        cprint("=" * 50, "green")
        cprint(f"动作MSE误差: {mean_mse:.6f} ± {std_mse:.6f}", "cyan")
        cprint(f"模型损失: {mean_loss:.6f} ± {std_loss:.6f}", "cyan")
        cprint(f"评估样本数: {len(all_mse_errors)}", "cyan")
        
        # 保存结果
        results = {
            'mean_mse_error': mean_mse,
            'std_mse_error': std_mse,
            'mean_loss': mean_loss,
            'std_loss': std_loss,
            'num_samples': len(all_mse_errors),
            'all_mse_errors': all_mse_errors,
            'all_losses': all_losses
        }
        
        # 保存到文件
        output_dir = pathlib.Path(workspace.output_dir)
        results_file = output_dir / 'evaluation_results.npz'
        np.savez(results_file, **results)
        cprint(f"结果已保存到: {results_file}", "green")
        
        return results
    else:
        cprint("评估失败：没有成功处理的批次", "red")
        return None


@hydra.main(
    version_base=None,
    config_path=str(pathlib.Path(__file__).parent.joinpath(
        'diffusion_policy_3d','config'))
)
def main(cfg: OmegaConf):
    torch.manual_seed(42)
    # resolve immediately so all the ${now:} resolvers
    # will use the same time.
    OmegaConf.resolve(cfg)
    
    cprint(f"配置文件: {cfg._target_}", "yellow")
    cprint(f"任务: {cfg.task_name}", "yellow")
    cprint(f"数据路径: {cfg.task.dataset.zarr_path}", "yellow")

    # 获取输出目录
    from hydra.core.hydra_config import HydraConfig
    hydra_cfg = HydraConfig.get()
    output_dir = hydra_cfg.runtime.output_dir
    cprint(f"输出目录: {output_dir}", "yellow")
    
    cls = hydra.utils.get_class(cfg._target_)
    workspace: BaseWorkspace = cls(cfg)
    
    # 检查模型文件是否存在
    checkpoint_path = workspace.get_checkpoint_path(tag='latest')
    if not checkpoint_path.exists():
        cprint(f"错误: 找不到模型文件 {checkpoint_path}", "red")
        cprint("请检查 hydra.run.dir 路径是否正确", "red")
        return
    
    cprint(f"使用模型: {checkpoint_path}", "green")
    
    # 开始评估
    results = evaluate_model_on_dataset(workspace, cfg)
    
    if results:
        cprint("评估完成！", "green")
    else:
        cprint("评估失败！", "red")


if __name__ == "__main__":
    main()
