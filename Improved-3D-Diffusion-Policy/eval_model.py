#!/usr/bin/env python3

import sys
# use line-buffering for both stdout and stderr
sys.stdout = open(sys.stdout.fileno(), mode='w', buffering=1)
sys.stderr = open(sys.stderr.fileno(), mode='w', buffering=1)

import hydra
import time
from omegaconf import OmegaConf
import pathlib
from diffusion_policy_3d.workspace.base_workspace import BaseWorkspace
import tqdm
import torch
import os 
import numpy as np
from termcolor import cprint
from torch.utils.data import DataLoader
from diffusion_policy_3d.common.pytorch_util import dict_apply
import copy

os.environ['WANDB_SILENT'] = "True"
# allows arbitrary python code execution in configs using the ${eval:''} resolver
OmegaConf.register_new_resolver("eval", eval, replace=True)


def analyze_trajectories(pred_traj, gt_traj):
    """
    详细分析预测轨迹与真实轨迹的差异

    Args:
        pred_traj: 预测轨迹 [N, T, action_dim]
        gt_traj: 真实轨迹 [N, T, action_dim]
    """
    cprint("=" * 50, "cyan")
    cprint("轨迹级别详细分析", "cyan")
    cprint("=" * 50, "cyan")

    N, T, action_dim = pred_traj.shape
    cprint(f"轨迹数量: {N}, 时间步长: {T}, 动作维度: {action_dim}", "white")

    # 1. 整体轨迹误差
    trajectory_mse = np.mean((pred_traj - gt_traj) ** 2, axis=(1, 2))  # [N,]
    overall_mse = np.mean(trajectory_mse)

    # 2. 每个时间步的误差
    timestep_mse = np.mean((pred_traj - gt_traj) ** 2, axis=(0, 2))  # [T,]

    # 3. 每个动作维度的误差
    action_dim_mse = np.mean((pred_traj - gt_traj) ** 2, axis=(0, 1))  # [action_dim,]

    # 4. 轨迹长度分析（累积距离）
    def compute_trajectory_length(traj):
        # 计算相邻时间步之间的欧几里得距离
        diff = np.diff(traj, axis=1)  # [N, T-1, action_dim]
        distances = np.sqrt(np.sum(diff ** 2, axis=2))  # [N, T-1]
        return np.sum(distances, axis=1)  # [N,]

    pred_lengths = compute_trajectory_length(pred_traj)
    gt_lengths = compute_trajectory_length(gt_traj)
    length_diff = pred_lengths - gt_lengths

    # 5. 轨迹平滑度分析（加速度）
    def compute_smoothness(traj):
        # 计算二阶差分（加速度）
        acc = np.diff(traj, n=2, axis=1)  # [N, T-2, action_dim]
        return np.mean(np.sqrt(np.sum(acc ** 2, axis=2)), axis=1)  # [N,]

    pred_smoothness = compute_smoothness(pred_traj)
    gt_smoothness = compute_smoothness(gt_traj)

    # 打印分析结果
    cprint(f"整体轨迹MSE: {overall_mse:.6f}", "green")
    cprint(f"最好的轨迹MSE: {np.min(trajectory_mse):.6f}", "green")
    cprint(f"最差的轨迹MSE: {np.max(trajectory_mse):.6f}", "green")

    cprint(f"\n时间步误差分析:", "yellow")
    for t in range(min(T, 10)):  # 只显示前10个时间步
        cprint(f"  时间步 {t}: MSE = {timestep_mse[t]:.6f}", "white")

    cprint(f"\n动作维度误差分析 (前10维):", "yellow")
    for i in range(min(action_dim, 10)):
        cprint(f"  动作维度 {i}: MSE = {action_dim_mse[i]:.6f}", "white")

    cprint(f"\n轨迹长度分析:", "yellow")
    cprint(f"  预测轨迹平均长度: {np.mean(pred_lengths):.4f}", "white")
    cprint(f"  真实轨迹平均长度: {np.mean(gt_lengths):.4f}", "white")
    cprint(f"  长度差异: {np.mean(length_diff):.4f} ± {np.std(length_diff):.4f}", "white")

    cprint(f"\n轨迹平滑度分析:", "yellow")
    cprint(f"  预测轨迹平滑度: {np.mean(pred_smoothness):.4f}", "white")
    cprint(f"  真实轨迹平滑度: {np.mean(gt_smoothness):.4f}", "white")

    return {
        'trajectory_mse_per_sample': trajectory_mse,
        'overall_trajectory_mse': overall_mse,
        'timestep_mse': timestep_mse,
        'action_dim_mse': action_dim_mse,
        'pred_trajectory_lengths': pred_lengths,
        'gt_trajectory_lengths': gt_lengths,
        'trajectory_length_diff': length_diff,
        'pred_smoothness': pred_smoothness,
        'gt_smoothness': gt_smoothness
    }


def evaluate_model_on_dataset(workspace, cfg):
    """
    在数据集上评估训练好的模型
    """
    cprint("=" * 50, "green")
    cprint("开始模型评估", "green")
    cprint("=" * 50, "green")
    
    # 加载模型
    policy = workspace.get_model()
    policy.eval()

    # 确保模型在正确的设备上
    device = torch.device(cfg.training.device)
    policy = policy.to(device)
    
    # 配置数据集
    dataset = hydra.utils.instantiate(cfg.task.dataset)
    
    # 获取验证数据集
    val_dataset = dataset.get_validation_dataset()
    if len(val_dataset) == 0:
        cprint("警告: 验证数据集为空，使用训练数据集进行评估", "yellow")
        val_dataset = dataset
    
    val_dataloader = DataLoader(val_dataset, **cfg.val_dataloader)
    
    cprint(f"数据集大小: {len(val_dataset)}", "cyan")
    cprint(f"批次大小: {cfg.val_dataloader.batch_size}", "cyan")
    
    # 评估指标
    all_mse_errors = []
    all_losses = []

    # 轨迹级别的比较数据
    all_pred_trajectories = []
    all_gt_trajectories = []
    all_obs_data = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm.tqdm(val_dataloader, desc="评估中")):
            # 数据转移到设备 - 递归处理嵌套字典
            def to_device_recursive(obj):
                if isinstance(obj, torch.Tensor):
                    return obj.to(device, non_blocking=True)
                elif isinstance(obj, dict):
                    return {k: to_device_recursive(v) for k, v in obj.items()}
                elif isinstance(obj, (list, tuple)):
                    return type(obj)(to_device_recursive(item) for item in obj)
                else:
                    return obj

            batch = to_device_recursive(batch)
            
            obs_dict = batch['obs']
            gt_action = batch['action']
            
            # 模型推理
            try:
                # 检查数据设备
                for key, value in obs_dict.items():
                    if isinstance(value, torch.Tensor):
                        if value.device != device:
                            cprint(f"警告: {key} 在设备 {value.device}，期望在 {device}", "yellow")

                result = policy.predict_action(obs_dict)
                pred_action = result['action_pred']

                # 保存轨迹数据用于详细分析
                all_pred_trajectories.append(pred_action.cpu().numpy())
                all_gt_trajectories.append(gt_action.cpu().numpy())

                # 保存观测数据
                obs_for_save = {}
                for key, value in obs_dict.items():
                    if isinstance(value, torch.Tensor):
                        obs_for_save[key] = value.cpu().numpy()
                    else:
                        obs_for_save[key] = value
                all_obs_data.append(obs_for_save)

                # 计算MSE误差
                mse = torch.nn.functional.mse_loss(pred_action, gt_action)
                all_mse_errors.append(mse.item())

                # 计算模型损失
                loss_result = policy.compute_loss(batch)
                if isinstance(loss_result, tuple):
                    loss = loss_result[0]  # 取第一个元素（通常是损失值）
                else:
                    loss = loss_result
                all_losses.append(loss.item())

                if batch_idx == 0:
                    cprint(f"第一个批次成功处理", "green")

            except Exception as e:
                cprint(f"批次 {batch_idx} 评估失败: {e}", "red")
                import traceback
                cprint(f"详细错误: {traceback.format_exc()}", "red")
                continue
            
            # 限制评估批次数量（可选）
            if cfg.training.max_val_steps is not None and batch_idx >= cfg.training.max_val_steps - 1:
                break
    
    # 计算统计结果
    if all_mse_errors:
        mean_mse = np.mean(all_mse_errors)
        std_mse = np.std(all_mse_errors)
        mean_loss = np.mean(all_losses)
        std_loss = np.std(all_losses)
        
        cprint("=" * 50, "green")
        cprint("评估结果", "green")
        cprint("=" * 50, "green")
        cprint(f"动作MSE误差: {mean_mse:.6f} ± {std_mse:.6f}", "cyan")
        cprint(f"模型损失: {mean_loss:.6f} ± {std_loss:.6f}", "cyan")
        cprint(f"评估样本数: {len(all_mse_errors)}", "cyan")
        
        # 合并所有轨迹数据
        all_pred_trajectories = np.concatenate(all_pred_trajectories, axis=0)
        all_gt_trajectories = np.concatenate(all_gt_trajectories, axis=0)

        # 计算轨迹级别的详细指标
        trajectory_analysis = analyze_trajectories(all_pred_trajectories, all_gt_trajectories)

        # 保存结果
        results = {
            'mean_mse_error': mean_mse,
            'std_mse_error': std_mse,
            'mean_loss': mean_loss,
            'std_loss': std_loss,
            'num_samples': len(all_mse_errors),
            'all_mse_errors': all_mse_errors,
            'all_losses': all_losses,
            # 轨迹数据
            'pred_trajectories': all_pred_trajectories,
            'gt_trajectories': all_gt_trajectories,
            'obs_data': all_obs_data,
            # 轨迹分析
            **trajectory_analysis
        }
        
        # 保存到文件
        output_dir = pathlib.Path(workspace.output_dir)
        results_file = output_dir / 'evaluation_results.npz'
        np.savez(results_file, **results)
        cprint(f"结果已保存到: {results_file}", "green")
        
        return results
    else:
        cprint("评估失败：没有成功处理的批次", "red")
        return None


@hydra.main(
    version_base=None,
    config_path=str(pathlib.Path(__file__).parent.joinpath(
        'diffusion_policy_3d','config'))
)
def main(cfg: OmegaConf):
    torch.manual_seed(42)
    # resolve immediately so all the ${now:} resolvers
    # will use the same time.
    OmegaConf.resolve(cfg)
    
    cprint(f"配置文件: {cfg._target_}", "yellow")
    cprint(f"任务: {cfg.task_name}", "yellow")
    cprint(f"数据路径: {cfg.task.dataset.zarr_path}", "yellow")

    # 获取输出目录
    from hydra.core.hydra_config import HydraConfig
    hydra_cfg = HydraConfig.get()
    output_dir = hydra_cfg.runtime.output_dir
    cprint(f"输出目录: {output_dir}", "yellow")
    
    cls = hydra.utils.get_class(cfg._target_)
    workspace: BaseWorkspace = cls(cfg)
    
    # 检查模型文件是否存在
    checkpoint_path = workspace.get_checkpoint_path(tag='latest')
    if not checkpoint_path.exists():
        cprint(f"错误: 找不到模型文件 {checkpoint_path}", "red")
        cprint("请检查 hydra.run.dir 路径是否正确", "red")
        return
    
    cprint(f"使用模型: {checkpoint_path}", "green")
    
    # 开始评估
    results = evaluate_model_on_dataset(workspace, cfg)
    
    if results:
        cprint("评估完成！", "green")
    else:
        cprint("评估失败！", "red")


if __name__ == "__main__":
    main()
