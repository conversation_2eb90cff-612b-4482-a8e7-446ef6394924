#!/usr/bin/env python3

import numpy as np
import matplotlib.pyplot as plt
import argparse
from pathlib import Path
import seaborn as sns
from termcolor import cprint

# Set matplotlib style
plt.style.use('default')
sns.set_palette("husl")

def load_joint_angle_results(results_path):
    """Load joint angle evaluation results"""
    results = np.load(results_path, allow_pickle=True)
    return results

def plot_all_joints_comparison(pred_actions, gt_actions, joint_names, save_dir, num_samples=3):
    """
    Plot comparison of all 25 joints: Red=Predicted, Green=Ground Truth
    25 dimensions = 25 rows of plots
    """
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    N, T, action_dim = pred_actions.shape
    
    # Select random samples for visualization
    sample_indices = np.random.choice(N, min(num_samples, N), replace=False)
    
    for sample_idx in sample_indices:
        # Create figure with 25 rows (one for each joint)
        fig, axes = plt.subplots(25, 1, figsize=(12, 50))
        fig.suptitle(f'Joint Angle Comparison - Sample {sample_idx}\nRed: Predicted, Green: Ground Truth', 
                     fontsize=16, y=0.995)
        
        for joint_idx in range(action_dim):
            ax = axes[joint_idx]
            
            # Plot predicted (red) and ground truth (green)
            time_steps = np.arange(T)
            pred_angles = np.degrees(pred_actions[sample_idx, :, joint_idx])
            gt_angles = np.degrees(gt_actions[sample_idx, :, joint_idx])
            
            ax.plot(time_steps, pred_angles, 'r-', linewidth=2, label='Predicted', alpha=0.8)
            ax.plot(time_steps, gt_angles, 'g-', linewidth=2, label='Ground Truth', alpha=0.8)
            
            # Set labels and title
            joint_name = joint_names[joint_idx] if joint_idx < len(joint_names) else f"Joint_{joint_idx}"
            ax.set_title(f'{joint_name}', fontsize=12, pad=5)
            ax.set_ylabel('Angle (deg)', fontsize=10)
            ax.grid(True, alpha=0.3)
            
            # Only show legend on first subplot
            if joint_idx == 0:
                ax.legend(loc='upper right', fontsize=10)
            
            # Only show x-label on last subplot
            if joint_idx == action_dim - 1:
                ax.set_xlabel('Time Step', fontsize=10)
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.98)
        plt.savefig(save_dir / f'all_joints_sample_{sample_idx}.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        cprint(f"Joint comparison plot saved for sample {sample_idx}", "green")
    
    cprint(f"All joint comparison plots saved to: {save_dir}", "green")

def plot_joint_error_analysis(results, save_dir):
    """Plot detailed joint error analysis"""
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    joint_names = results['joint_names']
    joint_mae = results['joint_mae']
    joint_rmse = results['joint_rmse']
    timestep_mae = results['timestep_mae']
    
    # Create comprehensive error analysis plot
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Per-joint MAE (in degrees)
    joint_mae_deg = np.degrees(joint_mae)
    bars1 = axes[0, 0].bar(range(len(joint_mae_deg)), joint_mae_deg, alpha=0.7)
    
    # Color best and worst joints
    best_joint = np.argmin(joint_mae_deg)
    worst_joint = np.argmax(joint_mae_deg)
    bars1[best_joint].set_color('green')
    bars1[worst_joint].set_color('red')
    
    axes[0, 0].set_title('Mean Absolute Error per Joint', fontsize=14)
    axes[0, 0].set_xlabel('Joint Index')
    axes[0, 0].set_ylabel('MAE (degrees)')
    axes[0, 0].set_xticks(range(len(joint_names)))
    axes[0, 0].set_xticklabels([f'J{i}' for i in range(len(joint_names))], rotation=45)
    axes[0, 0].grid(True, alpha=0.3)
    
    # Add annotations for best and worst
    axes[0, 0].annotate(f'Best: {joint_names[best_joint]}\n{joint_mae_deg[best_joint]:.2f}°', 
                        xy=(best_joint, joint_mae_deg[best_joint]), 
                        xytext=(best_joint, joint_mae_deg[best_joint] + np.max(joint_mae_deg)*0.1),
                        arrowprops=dict(arrowstyle='->', color='green'),
                        ha='center', color='green', fontweight='bold')
    
    axes[0, 0].annotate(f'Worst: {joint_names[worst_joint]}\n{joint_mae_deg[worst_joint]:.2f}°', 
                        xy=(worst_joint, joint_mae_deg[worst_joint]), 
                        xytext=(worst_joint, joint_mae_deg[worst_joint] + np.max(joint_mae_deg)*0.2),
                        arrowprops=dict(arrowstyle='->', color='red'),
                        ha='center', color='red', fontweight='bold')
    
    # 2. Per-timestep MAE
    timestep_mae_deg = np.degrees(timestep_mae)
    axes[0, 1].plot(range(len(timestep_mae_deg)), timestep_mae_deg, 'o-', linewidth=2, markersize=6)
    axes[0, 1].set_title('Mean Absolute Error per Timestep', fontsize=14)
    axes[0, 1].set_xlabel('Time Step')
    axes[0, 1].set_ylabel('MAE (degrees)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. Joint error distribution
    axes[1, 0].hist(joint_mae_deg, bins=15, alpha=0.7, edgecolor='black')
    axes[1, 0].axvline(np.mean(joint_mae_deg), color='red', linestyle='--', 
                       label=f'Mean: {np.mean(joint_mae_deg):.2f}°')
    axes[1, 0].axvline(np.median(joint_mae_deg), color='blue', linestyle='--', 
                       label=f'Median: {np.median(joint_mae_deg):.2f}°')
    axes[1, 0].set_title('Distribution of Joint Errors', fontsize=14)
    axes[1, 0].set_xlabel('MAE (degrees)')
    axes[1, 0].set_ylabel('Number of Joints')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. Joint names table
    axes[1, 1].axis('off')
    
    # Create joint names table
    table_data = []
    for i, (name, mae, rmse) in enumerate(zip(joint_names, joint_mae_deg, np.degrees(joint_rmse))):
        table_data.append([f'J{i}', name, f'{mae:.2f}°', f'{rmse:.2f}°'])
    
    # Split into two columns if too many joints
    if len(table_data) > 12:
        mid = len(table_data) // 2
        left_data = table_data[:mid]
        right_data = table_data[mid:]
        
        # Left column
        table_text = "Joint Index | Joint Name | MAE | RMSE\n" + "-" * 40 + "\n"
        for row in left_data:
            table_text += f"{row[0]:<5} | {row[1]:<20} | {row[2]:<6} | {row[3]:<6}\n"
        
        axes[1, 1].text(0.05, 0.95, table_text, transform=axes[1, 1].transAxes, 
                        fontsize=8, verticalalignment='top', fontfamily='monospace')
        
        # Right column
        table_text = "Joint Index | Joint Name | MAE | RMSE\n" + "-" * 40 + "\n"
        for row in right_data:
            table_text += f"{row[0]:<5} | {row[1]:<20} | {row[2]:<6} | {row[3]:<6}\n"
        
        axes[1, 1].text(0.55, 0.95, table_text, transform=axes[1, 1].transAxes, 
                        fontsize=8, verticalalignment='top', fontfamily='monospace')
    else:
        table_text = "Joint Index | Joint Name | MAE | RMSE\n" + "-" * 40 + "\n"
        for row in table_data:
            table_text += f"{row[0]:<5} | {row[1]:<20} | {row[2]:<6} | {row[3]:<6}\n"
        
        axes[1, 1].text(0.05, 0.95, table_text, transform=axes[1, 1].transAxes, 
                        fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    axes[1, 1].set_title('Joint Error Summary', fontsize=14)
    
    plt.tight_layout()
    plt.savefig(save_dir / 'joint_error_analysis.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    cprint(f"Joint error analysis plot saved to: {save_dir}", "green")

def create_error_report(results, save_dir):
    """Create detailed text report"""
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    joint_names = results['joint_names']
    joint_mae = results['joint_mae']
    joint_rmse = results['joint_rmse']
    overall_mae = results['overall_mae']
    overall_rmse = results['overall_rmse']
    
    with open(save_dir / 'joint_angle_report.txt', 'w') as f:
        f.write("=" * 80 + "\n")
        f.write("JOINT ANGLE EVALUATION REPORT\n")
        f.write("=" * 80 + "\n\n")
        
        f.write(f"Overall Performance:\n")
        f.write(f"  Mean Absolute Error: {overall_mae:.4f} rad ({np.degrees(overall_mae):.2f} deg)\n")
        f.write(f"  Root Mean Square Error: {overall_rmse:.4f} rad ({np.degrees(overall_rmse):.2f} deg)\n\n")
        
        f.write("Per-Joint Analysis:\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'Index':<6} {'Joint Name':<25} {'MAE (rad)':<12} {'MAE (deg)':<12} {'RMSE (rad)':<12} {'RMSE (deg)':<12}\n")
        f.write("-" * 80 + "\n")
        
        for i, name in enumerate(joint_names):
            f.write(f"{i:<6} {name:<25} {joint_mae[i]:<12.4f} {np.degrees(joint_mae[i]):<12.2f} "
                   f"{joint_rmse[i]:<12.4f} {np.degrees(joint_rmse[i]):<12.2f}\n")
        
        # Best and worst joints
        best_joint = np.argmin(joint_mae)
        worst_joint = np.argmax(joint_mae)
        
        f.write(f"\nBest Performing Joint:\n")
        f.write(f"  {joint_names[best_joint]} (Index {best_joint}): {np.degrees(joint_mae[best_joint]):.2f}° MAE\n")
        
        f.write(f"\nWorst Performing Joint:\n")
        f.write(f"  {joint_names[worst_joint]} (Index {worst_joint}): {np.degrees(joint_mae[worst_joint]):.2f}° MAE\n")
    
    cprint(f"Detailed report saved to: {save_dir / 'joint_angle_report.txt'}", "green")

def main():
    parser = argparse.ArgumentParser(description='Visualize joint angle evaluation results')
    parser.add_argument('--results_path', type=str, required=True,
                        help='Path to joint angle evaluation results (.npz)')
    parser.add_argument('--output_dir', type=str, default='joint_angle_visualization',
                        help='Output directory for plots')
    
    args = parser.parse_args()
    
    # Load results
    cprint(f"Loading joint angle results: {args.results_path}", "yellow")
    results = load_joint_angle_results(args.results_path)
    
    # Check available keys
    cprint("Available data:", "cyan")
    for key in results.files:
        data = results[key]
        if hasattr(data, 'shape'):
            cprint(f"  {key}: shape={data.shape}, dtype={data.dtype}", "white")
        else:
            cprint(f"  {key}: {type(data)}", "white")
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Extract data
    pred_actions = results['pred_actions']
    gt_actions = results['gt_actions']
    joint_names = results['joint_names']
    
    cprint(f"Data shape: {pred_actions.shape}", "cyan")
    cprint(f"Joint names: {len(joint_names)} joints", "cyan")
    
    # Generate visualizations
    plot_all_joints_comparison(pred_actions, gt_actions, joint_names, output_dir / 'joint_comparisons')
    plot_joint_error_analysis(results, output_dir)
    create_error_report(results, output_dir)
    
    cprint("=" * 60, "green")
    cprint("Joint angle visualization completed!", "green")
    cprint(f"All outputs saved to: {output_dir}", "green")
    cprint("=" * 60, "green")

if __name__ == "__main__":
    main()
