#!/usr/bin/env python3

import numpy as np
import matplotlib.pyplot as plt
import argparse
from pathlib import Path
import seaborn as sns
from termcolor import cprint

def load_evaluation_results(results_path):
    """加载评估结果"""
    results = np.load(results_path, allow_pickle=True)
    return results

def plot_trajectory_comparison(pred_traj, gt_traj, save_dir, num_samples=5):
    """绘制轨迹对比图"""
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    N, T, action_dim = pred_traj.shape
    
    # 随机选择几个样本进行可视化
    sample_indices = np.random.choice(N, min(num_samples, N), replace=False)
    
    for idx, sample_idx in enumerate(sample_indices):
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle(f'轨迹对比 - 样本 {sample_idx}', fontsize=16)
        
        # 选择前6个动作维度进行可视化
        for i in range(min(6, action_dim)):
            row = i // 3
            col = i % 3
            
            ax = axes[row, col]
            ax.plot(pred_traj[sample_idx, :, i], 'r-', label='预测', linewidth=2)
            ax.plot(gt_traj[sample_idx, :, i], 'b-', label='真实', linewidth=2)
            ax.set_title(f'动作维度 {i}')
            ax.set_xlabel('时间步')
            ax.set_ylabel('动作值')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_dir / f'trajectory_comparison_sample_{sample_idx}.png', dpi=150)
        plt.close()
        
    cprint(f"轨迹对比图已保存到: {save_dir}", "green")

def plot_error_analysis(results, save_dir):
    """绘制误差分析图"""
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    # 1. 时间步误差分析
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    timestep_mse = results['timestep_mse']
    plt.plot(timestep_mse, 'o-', linewidth=2, markersize=6)
    plt.title('各时间步MSE误差')
    plt.xlabel('时间步')
    plt.ylabel('MSE')
    plt.grid(True, alpha=0.3)
    
    # 2. 动作维度误差分析
    plt.subplot(1, 3, 2)
    action_dim_mse = results['action_dim_mse']
    plt.bar(range(len(action_dim_mse)), action_dim_mse)
    plt.title('各动作维度MSE误差')
    plt.xlabel('动作维度')
    plt.ylabel('MSE')
    plt.xticks(range(0, len(action_dim_mse), max(1, len(action_dim_mse)//10)))
    
    # 3. 轨迹MSE分布
    plt.subplot(1, 3, 3)
    trajectory_mse = results['trajectory_mse_per_sample']
    plt.hist(trajectory_mse, bins=20, alpha=0.7, edgecolor='black')
    plt.title('轨迹MSE分布')
    plt.xlabel('MSE')
    plt.ylabel('频次')
    plt.axvline(np.mean(trajectory_mse), color='red', linestyle='--', 
                label=f'平均值: {np.mean(trajectory_mse):.4f}')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(save_dir / 'error_analysis.png', dpi=150)
    plt.close()
    
    cprint(f"误差分析图已保存到: {save_dir}", "green")

def plot_trajectory_properties(results, save_dir):
    """绘制轨迹属性分析"""
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. 轨迹长度对比
    pred_lengths = results['pred_trajectory_lengths']
    gt_lengths = results['gt_trajectory_lengths']
    
    axes[0, 0].scatter(gt_lengths, pred_lengths, alpha=0.6)
    axes[0, 0].plot([gt_lengths.min(), gt_lengths.max()], 
                    [gt_lengths.min(), gt_lengths.max()], 'r--', label='理想线')
    axes[0, 0].set_xlabel('真实轨迹长度')
    axes[0, 0].set_ylabel('预测轨迹长度')
    axes[0, 0].set_title('轨迹长度对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 轨迹长度差异分布
    length_diff = results['trajectory_length_diff']
    axes[0, 1].hist(length_diff, bins=20, alpha=0.7, edgecolor='black')
    axes[0, 1].axvline(0, color='red', linestyle='--', label='无差异')
    axes[0, 1].axvline(np.mean(length_diff), color='green', linestyle='--', 
                       label=f'平均差异: {np.mean(length_diff):.3f}')
    axes[0, 1].set_xlabel('长度差异 (预测 - 真实)')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].set_title('轨迹长度差异分布')
    axes[0, 1].legend()
    
    # 3. 轨迹平滑度对比
    pred_smoothness = results['pred_smoothness']
    gt_smoothness = results['gt_smoothness']
    
    axes[1, 0].scatter(gt_smoothness, pred_smoothness, alpha=0.6)
    axes[1, 0].plot([gt_smoothness.min(), gt_smoothness.max()], 
                    [gt_smoothness.min(), gt_smoothness.max()], 'r--', label='理想线')
    axes[1, 0].set_xlabel('真实轨迹平滑度')
    axes[1, 0].set_ylabel('预测轨迹平滑度')
    axes[1, 0].set_title('轨迹平滑度对比')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 整体性能总结
    axes[1, 1].axis('off')
    
    # 计算统计信息
    mse_mean = results['overall_trajectory_mse']
    length_corr = np.corrcoef(pred_lengths, gt_lengths)[0, 1]
    smoothness_corr = np.corrcoef(pred_smoothness, gt_smoothness)[0, 1]
    
    summary_text = f"""
    性能总结:
    
    整体MSE: {mse_mean:.6f}
    
    轨迹长度相关性: {length_corr:.3f}
    平均长度差异: {np.mean(length_diff):.3f}
    
    平滑度相关性: {smoothness_corr:.3f}
    
    样本数量: {len(pred_lengths)}
    """
    
    axes[1, 1].text(0.1, 0.5, summary_text, fontsize=12, 
                     verticalalignment='center', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig(save_dir / 'trajectory_properties.png', dpi=150)
    plt.close()
    
    cprint(f"轨迹属性分析图已保存到: {save_dir}", "green")

def main():
    parser = argparse.ArgumentParser(description='可视化评估结果')
    parser.add_argument('--results_path', type=str, required=True,
                        help='评估结果文件路径 (.npz)')
    parser.add_argument('--output_dir', type=str, default='visualization_output',
                        help='输出目录')
    
    args = parser.parse_args()
    
    # 加载结果
    cprint(f"加载评估结果: {args.results_path}", "yellow")
    results = load_evaluation_results(args.results_path)
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # 提取轨迹数据
    pred_traj = results['pred_trajectories']
    gt_traj = results['gt_trajectories']
    
    cprint(f"轨迹数据形状: {pred_traj.shape}", "cyan")
    
    # 生成可视化
    plot_trajectory_comparison(pred_traj, gt_traj, output_dir / 'trajectories')
    plot_error_analysis(results, output_dir)
    plot_trajectory_properties(results, output_dir)
    
    cprint("=" * 50, "green")
    cprint("可视化完成！", "green")
    cprint(f"所有图片已保存到: {output_dir}", "green")
    cprint("=" * 50, "green")

if __name__ == "__main__":
    main()
