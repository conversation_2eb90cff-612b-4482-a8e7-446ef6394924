#!/usr/bin/env python3

import numpy as np
import matplotlib.pyplot as plt
import argparse
from pathlib import Path
import seaborn as sns
from termcolor import cprint

def load_evaluation_results(results_path):
    """加载评估结果"""
    results = np.load(results_path, allow_pickle=True)
    return results

def plot_trajectory_comparison(pred_traj, gt_traj, save_dir, num_samples=5):
    """绘制轨迹对比图"""
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    N, T, action_dim = pred_traj.shape
    
    # 随机选择几个样本进行可视化
    sample_indices = np.random.choice(N, min(num_samples, N), replace=False)

    for idx, sample_idx in enumerate(sample_indices):
        # 为每个样本创建所有维度的可视化
        # 计算需要的子图数量
        cols = 5  # 每行5个子图
        rows = (action_dim + cols - 1) // cols  # 向上取整

        fig, axes = plt.subplots(rows, cols, figsize=(20, 4*rows))
        fig.suptitle(f'所有动作维度轨迹对比 - 样本 {sample_idx}', fontsize=16)

        # 如果只有一行，确保axes是二维数组
        if rows == 1:
            axes = axes.reshape(1, -1)

        for i in range(action_dim):
            row = i // cols
            col = i % cols

            ax = axes[row, col]
            ax.plot(pred_traj[sample_idx, :, i], 'r-', label='预测', linewidth=2)
            ax.plot(gt_traj[sample_idx, :, i], 'b-', label='真实', linewidth=2)
            ax.set_title(f'动作维度 {i}')
            ax.set_xlabel('时间步')
            ax.set_ylabel('动作值')
            if i == 0:  # 只在第一个子图显示图例
                ax.legend()
            ax.grid(True, alpha=0.3)

        # 隐藏多余的子图
        for i in range(action_dim, rows * cols):
            row = i // cols
            col = i % cols
            axes[row, col].set_visible(False)

        plt.tight_layout()
        plt.savefig(save_dir / f'all_dimensions_sample_{sample_idx}.png', dpi=150, bbox_inches='tight')
        plt.close()
        
    cprint(f"轨迹对比图已保存到: {save_dir}", "green")

def plot_error_analysis(results, save_dir):
    """绘制误差分析图"""
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    # 1. 时间步误差分析
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    timestep_mse = results['timestep_mse']
    plt.plot(timestep_mse, 'o-', linewidth=2, markersize=6)
    plt.title('各时间步MSE误差')
    plt.xlabel('时间步')
    plt.ylabel('MSE')
    plt.grid(True, alpha=0.3)
    
    # 2. 动作维度误差分析
    plt.subplot(1, 3, 2)
    action_dim_mse = results['action_dim_mse']
    bars = plt.bar(range(len(action_dim_mse)), action_dim_mse)

    # 标记最好和最差的维度
    best_dim = np.argmin(action_dim_mse)
    worst_dim = np.argmax(action_dim_mse)
    bars[best_dim].set_color('green')
    bars[worst_dim].set_color('red')

    plt.title(f'各动作维度MSE误差 (共{len(action_dim_mse)}维)')
    plt.xlabel('动作维度')
    plt.ylabel('MSE')

    # 设置x轴刻度，显示所有维度但避免过于拥挤
    if len(action_dim_mse) <= 25:
        plt.xticks(range(len(action_dim_mse)))
    else:
        step = max(1, len(action_dim_mse) // 10)
        plt.xticks(range(0, len(action_dim_mse), step))

    plt.xticks(rotation=45)
    
    # 3. 轨迹MSE分布
    plt.subplot(1, 3, 3)
    trajectory_mse = results['trajectory_mse_per_sample']
    plt.hist(trajectory_mse, bins=20, alpha=0.7, edgecolor='black')
    plt.title('轨迹MSE分布')
    plt.xlabel('MSE')
    plt.ylabel('频次')
    plt.axvline(np.mean(trajectory_mse), color='red', linestyle='--', 
                label=f'平均值: {np.mean(trajectory_mse):.4f}')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(save_dir / 'error_analysis.png', dpi=150)
    plt.close()
    
    cprint(f"误差分析图已保存到: {save_dir}", "green")

def plot_trajectory_properties(results, save_dir):
    """绘制轨迹属性分析"""
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. 轨迹长度对比
    pred_lengths = results['pred_trajectory_lengths']
    gt_lengths = results['gt_trajectory_lengths']
    
    axes[0, 0].scatter(gt_lengths, pred_lengths, alpha=0.6)
    axes[0, 0].plot([gt_lengths.min(), gt_lengths.max()], 
                    [gt_lengths.min(), gt_lengths.max()], 'r--', label='理想线')
    axes[0, 0].set_xlabel('真实轨迹长度')
    axes[0, 0].set_ylabel('预测轨迹长度')
    axes[0, 0].set_title('轨迹长度对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 轨迹长度差异分布
    length_diff = results['trajectory_length_diff']
    axes[0, 1].hist(length_diff, bins=20, alpha=0.7, edgecolor='black')
    axes[0, 1].axvline(0, color='red', linestyle='--', label='无差异')
    axes[0, 1].axvline(np.mean(length_diff), color='green', linestyle='--', 
                       label=f'平均差异: {np.mean(length_diff):.3f}')
    axes[0, 1].set_xlabel('长度差异 (预测 - 真实)')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].set_title('轨迹长度差异分布')
    axes[0, 1].legend()
    
    # 3. 轨迹平滑度对比
    pred_smoothness = results['pred_smoothness']
    gt_smoothness = results['gt_smoothness']
    
    axes[1, 0].scatter(gt_smoothness, pred_smoothness, alpha=0.6)
    axes[1, 0].plot([gt_smoothness.min(), gt_smoothness.max()], 
                    [gt_smoothness.min(), gt_smoothness.max()], 'r--', label='理想线')
    axes[1, 0].set_xlabel('真实轨迹平滑度')
    axes[1, 0].set_ylabel('预测轨迹平滑度')
    axes[1, 0].set_title('轨迹平滑度对比')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 整体性能总结
    axes[1, 1].axis('off')
    
    # 计算统计信息
    mse_mean = results['overall_trajectory_mse']
    length_corr = np.corrcoef(pred_lengths, gt_lengths)[0, 1]
    smoothness_corr = np.corrcoef(pred_smoothness, gt_smoothness)[0, 1]
    
    summary_text = f"""
    性能总结:
    
    整体MSE: {mse_mean:.6f}
    
    轨迹长度相关性: {length_corr:.3f}
    平均长度差异: {np.mean(length_diff):.3f}
    
    平滑度相关性: {smoothness_corr:.3f}
    
    样本数量: {len(pred_lengths)}
    """
    
    axes[1, 1].text(0.1, 0.5, summary_text, fontsize=12, 
                     verticalalignment='center', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig(save_dir / 'trajectory_properties.png', dpi=150)
    plt.close()
    
    cprint(f"轨迹属性分析图已保存到: {save_dir}", "green")

def plot_all_dimensions_analysis(pred_traj, gt_traj, results, save_dir):
    """创建所有动作维度的详细分析"""
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)

    N, T, action_dim = pred_traj.shape
    action_dim_mse = results['action_dim_mse']

    # 创建大图显示所有维度的误差
    fig, axes = plt.subplots(2, 1, figsize=(20, 12))

    # 上图：所有维度的MSE误差柱状图
    bars = axes[0].bar(range(action_dim), action_dim_mse, alpha=0.7)

    # 标记最好和最差的维度
    best_dim = np.argmin(action_dim_mse)
    worst_dim = np.argmax(action_dim_mse)
    bars[best_dim].set_color('green')
    bars[worst_dim].set_color('red')

    axes[0].set_title(f'所有{action_dim}个动作维度的MSE误差', fontsize=16)
    axes[0].set_xlabel('动作维度')
    axes[0].set_ylabel('MSE误差')
    axes[0].grid(True, alpha=0.3)

    # 添加最值标注
    axes[0].annotate(f'最佳: {best_dim}\nMSE={action_dim_mse[best_dim]:.6f}',
                     xy=(best_dim, action_dim_mse[best_dim]),
                     xytext=(best_dim, action_dim_mse[best_dim] + np.max(action_dim_mse)*0.1),
                     arrowprops=dict(arrowstyle='->', color='green'),
                     ha='center', color='green', fontweight='bold')

    axes[0].annotate(f'最差: {worst_dim}\nMSE={action_dim_mse[worst_dim]:.6f}',
                     xy=(worst_dim, action_dim_mse[worst_dim]),
                     xytext=(worst_dim, action_dim_mse[worst_dim] + np.max(action_dim_mse)*0.2),
                     arrowprops=dict(arrowstyle='->', color='red'),
                     ha='center', color='red', fontweight='bold')

    # 下图：误差分布的统计信息
    axes[1].hist(action_dim_mse, bins=20, alpha=0.7, edgecolor='black')
    axes[1].axvline(np.mean(action_dim_mse), color='blue', linestyle='--',
                    label=f'平均值: {np.mean(action_dim_mse):.6f}')
    axes[1].axvline(np.median(action_dim_mse), color='orange', linestyle='--',
                    label=f'中位数: {np.median(action_dim_mse):.6f}')
    axes[1].set_title('动作维度MSE误差分布', fontsize=16)
    axes[1].set_xlabel('MSE误差')
    axes[1].set_ylabel('维度数量')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_dir / 'all_dimensions_analysis.png', dpi=150, bbox_inches='tight')
    plt.close()

    # 创建详细的维度信息表
    dimension_info = []
    for i in range(action_dim):
        dimension_info.append({
            'dimension': i,
            'mse': action_dim_mse[i],
            'rank': np.argsort(np.argsort(action_dim_mse))[i] + 1,  # 排名（1是最好）
            'percentile': (np.sum(action_dim_mse <= action_dim_mse[i]) / action_dim) * 100
        })

    # 保存详细信息到文本文件
    with open(save_dir / 'dimension_analysis.txt', 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("所有动作维度详细分析报告\n")
        f.write("=" * 80 + "\n\n")

        f.write(f"总维度数: {action_dim}\n")
        f.write(f"平均MSE: {np.mean(action_dim_mse):.6f}\n")
        f.write(f"标准差: {np.std(action_dim_mse):.6f}\n")
        f.write(f"最小MSE: {np.min(action_dim_mse):.6f} (维度 {best_dim})\n")
        f.write(f"最大MSE: {np.max(action_dim_mse):.6f} (维度 {worst_dim})\n")
        f.write(f"中位数MSE: {np.median(action_dim_mse):.6f}\n\n")

        f.write("各维度详细信息:\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'维度':>4} {'MSE误差':>12} {'排名':>6} {'百分位':>8}\n")
        f.write("-" * 80 + "\n")

        for info in dimension_info:
            f.write(f"{info['dimension']:>4} {info['mse']:>12.6f} {info['rank']:>6} {info['percentile']:>7.1f}%\n")

    cprint(f"所有维度分析图和报告已保存到: {save_dir}", "green")

def main():
    parser = argparse.ArgumentParser(description='可视化评估结果')
    parser.add_argument('--results_path', type=str, required=True,
                        help='评估结果文件路径 (.npz)')
    parser.add_argument('--output_dir', type=str, default='visualization_output',
                        help='输出目录')
    
    args = parser.parse_args()
    
    # 加载结果
    cprint(f"加载评估结果: {args.results_path}", "yellow")
    results = load_evaluation_results(args.results_path)

    # 检查结果文件中包含的键
    cprint("结果文件包含的数据:", "cyan")
    for key in results.files:
        data = results[key]
        if hasattr(data, 'shape'):
            cprint(f"  {key}: shape={data.shape}, dtype={data.dtype}", "white")
        else:
            cprint(f"  {key}: {type(data)}", "white")
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # 检查是否有轨迹数据
    if 'pred_trajectories' in results.files and 'gt_trajectories' in results.files:
        # 提取轨迹数据
        pred_traj = results['pred_trajectories']
        gt_traj = results['gt_trajectories']

        cprint(f"轨迹数据形状: {pred_traj.shape}", "cyan")

        # 生成可视化
        plot_trajectory_comparison(pred_traj, gt_traj, output_dir / 'trajectories')
        plot_error_analysis(results, output_dir)
        plot_trajectory_properties(results, output_dir)
        plot_all_dimensions_analysis(pred_traj, gt_traj, results, output_dir)
    else:
        cprint("警告: 结果文件中没有轨迹数据，请重新运行评估脚本", "red")
        cprint("只能显示基本的MSE和损失信息:", "yellow")

        # 显示基本信息
        if 'mean_mse_error' in results.files:
            cprint(f"平均MSE误差: {results['mean_mse_error'].item():.6f}", "green")
        if 'all_mse_errors' in results.files:
            mse_errors = results['all_mse_errors']
            cprint(f"各批次MSE误差: {mse_errors}", "green")
        if 'all_losses' in results.files:
            losses = results['all_losses']
            cprint(f"各批次损失: {losses}", "green")
    
    cprint("=" * 50, "green")
    cprint("可视化完成！", "green")
    cprint(f"所有图片已保存到: {output_dir}", "green")
    cprint("=" * 50, "green")

if __name__ == "__main__":
    main()
